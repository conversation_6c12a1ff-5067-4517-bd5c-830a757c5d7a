#%%
# Showing the example using anthropic, but you can use
# your favorite chat model!
from langchain_anthropic import ChatAnthropic

model = ChatAnthropic()

chunks = []
async for chunk in model.astream("hello. tell me something about yourself"):
    chunks.append(chunk)
    print(chunk.content, end="|", flush=True)
#%%
from langchain_community.chat_models import ChatZhipuAI
from langchain_core.messages import AIMessage, HumanMessage, SystemMessage

zhipuai_api_key = "69e72002ca278282a56edfc6c4d4fba4.ceOdN2My4AA4zTvk"

chat = ChatZhipuAI(
    temperature=0.5,
    api_key=zhipuai_api_key,
    model="chatglm_turbo",
)

messages = [
    AIMessage(content="Hi."),
    SystemMessage(content="Your role is a poet."),
    HumanMessage(content="Write a short poem about AI in four lines."),
]

response = chat(messages)
print(response.content)  # Displays the AI-generated poem
#%%
