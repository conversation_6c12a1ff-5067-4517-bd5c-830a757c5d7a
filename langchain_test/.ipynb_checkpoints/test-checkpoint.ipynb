#%%
from langchain_community.chat_models import ChatZhipuAI
from langchain_core.messages import AIMessage, HumanMessage, SystemMessage

zhipuai_api_key = "69e72002ca278282a56edfc6c4d4fba4.ceOdN2My4AA4zTvk"

chat = ChatZhipuAI(
    temperature=0.5,
    api_key=zhipuai_api_key,
    model="chatglm_turbo",
)

messages = [
    AIMessage(content="Hi."),
    SystemMessage(content="Your role is a poet."),
    HumanMessage(content="Write a short poem about AI in four lines."),
]

response = chat(messages)
print(response.content)  # Displays the AI-generated poem
#%%
import json
from langchain.llms.base import LLM
zhipuai_api_key = "69e72002ca278282a56edfc6c4d4fba4.ceOdN2My4AA4zTvk"
#%%

class ChatGLM4(LLM):
    max_token: int = 8192
    do_sample: bool = True
    temperature: float = 0.8
    top_p: float = 0.8
    tokenizer: object = None
    history: List = []
    tool_names: List = []
    has_search: bool = False
    client: object = None

    def __init__(self):
        super().__init__()
        self.client = ZhipuAI(api_key=zhipuai_api_key)

    @property
    def _llm_type(self) -> str:
        return "ChatGLM4"

    def stream(self, prompt:str, history=[]):
        if history is None:
            history = []
        
        history.append({"role": "user", "content": prompt})
        response = self.client.chat.completions.create(
            model="glm-4",
            messages=history,
            stream=True
        )
        for chunk in response:
            yield chunk.choices[0].delta.content

    def _tool_history(self, prompt: str):
        ans = []
        tool_prompt = prompt.split("You have access to the following tools:\n\n")[1].split("\n\nUse a json blob")[0].split("\n")

        tool_names = [tool.split(":")[0] for tool in tool_prompts]
        self.tool_names = tool_names
        tools_json = []
        for i, tool in enumerate(tool_names):
            tool_config = tool_config_from_file(tool)
            if tool_config:
                tools_json.append(tool_config)
            else:
                ValueError(f"Tool {tool} config not found! It's description is {tool_prompts[i]}")

        ans.append({
            "role": "system",
            "content": "Answer the following questions as best as you can. You have access to the following tools",
            "tools": tools_json
        })
        query = f"""{prompt.split("Human: ")[-1].strip()}"""
        return ans, query

def _extract_observation(self, prompt: str)
